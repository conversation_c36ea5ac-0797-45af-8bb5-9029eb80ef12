<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Cache;
use think\Db;
use think\Log;
/**
 * 游戏
 * @Authod Jw
 * @Time 2021/6/15
 * @package app\api\controller
 */
class Game extends Api
{
    protected $noNeedLogin = ['category','delete_log','get_game_sn','test','get_config','list_dcl_ast4'];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * @Method 分类列表
     * @Authod Jw
     * @Time 2021/6/21
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function category()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();

            $data = Db::name('game_category')->select();


            $this->success('成功获取',$data);
        }else{
            $this->error('请求方式不正确');
        }
    }

    /**
     * @Method 游戏列表
     * @Authod Jw
     * @Time 2021/6/21
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function list()
    {
        // post提交
        if ($this->request->isPost()) {
            try {
                // 接收传递过来的数据
                $params = $this->request->param();
                $where = [];

                // 获取用户信息
                $user_info = $this->auth->getUserinfo();
                if ($user_info['p_id']) {//代理ID
                    $where['agent_id'] = $user_info['p_id'];
                }

                if ($params) {
                    if (isset($params['c_id']) && $params['c_id'] > 0) {
                        $where['c_id'] = $params['c_id'];
                    }
                }
                $where['is_delete'] = 0;

                // 新增：只看可玩过滤
                $only_playable = isset($params['only_playable']) ? $params['only_playable'] : 0;

                $data = Db::name('game')
                    ->where($where)
                    ->where('status','>',0)
                    ->order('sort desc')
                    ->select();
                if (empty($data)) {
                    $this->success('成功获取', []);
                }

                // 获取所有游戏ID
                $gameIds = array_column($data, 'id');

                // 批量获取所有座位和玩家信息（1次查询）
                $allSeatsAndPlayers = Db::name('game_seat')
                    ->alias('s')
                    ->join(['fa_game_log l'],'s.id=l.seat_id and l.status in (0,1)','left')
                    ->join(['fa_user u'],'l.user_id=u.id','left')
                    ->where('s.game_id', 'in', $gameIds)
                    ->field('s.game_id,u.avatar,l.number')
                    ->select();

                // 按游戏ID分组座位数据
                $seatsByGame = [];
                foreach ($allSeatsAndPlayers as $seat) {
                    $seatsByGame[$seat['game_id']][] = [
                        'avatar' => $seat['avatar'] ? \config('site.domain').$seat['avatar'] : $seat['avatar'],
                        'number' => $seat['number']
                    ];
                }

                // 处理联机游戏逻辑
                $processedData = [];
                $unionGroups = [];

                foreach ($data as &$v) {
                    //游戏封面
                    $v['img'] = \config('site.domain').$v['img'];

                    // 设置座位数据（从批量查询结果中获取）
                    $v['player'] = isset($seatsByGame[$v['id']]) ? $seatsByGame[$v['id']] : [];
                    $hasEmptySeats = true;

                    // 如果开启了"只看可玩"过滤，检查用户是否可以玩这个游戏
                    if ($only_playable) {
                        // 检查金币是否足够
//                        if ($user_info['money'] < $v['coin']) {
//                            continue; // 跳过这个游戏
//                        }

                        // 检查VIP等级是否足够
                        if ($v['level'] > 0 && $user_info['level'] < $v['level']) {
                            continue; // 跳过这个游戏
                        }

                        // 检查是否有空位（基于已查询的player数据）
                        $hasEmptySeats = false;
                        foreach ($v['player'] as $player) {
                            if (empty($player['number'])) { // number为空表示空座位
                                $hasEmptySeats = true;
                                break;
                            }
                        }

                        if (!$hasEmptySeats && empty($v['union_tip'])) {
                            continue; // 跳过这个游戏
                        }
                    }

                    // 检查是否为联机游戏
                    if (!empty($v['union_tip'])) {
                        // 联机游戏：按union_tip分组
                        $unionTip = $v['union_tip'];
                        if (!isset($unionGroups[$unionTip])) {
                            $unionGroups[$unionTip] = [
                                'union_id' => 'union_' . $unionTip,
                                'union_name' => $v['union_name'],
                                'union_tip' => $unionTip,
                                'is_union' => true,
                                'img' => $v['img'], // 使用第一个游戏的图片
                                'coin' => $v['coin'], // 使用第一个游戏的金币
                                'level' => $v['level'], // 使用第一个游戏的VIP等级
                                'is_landscape' => $v['is_landscape'], // 使用第一个游戏的横竖屏设置
                                'max_players' => 4, // 联机游戏固定4人
                                'union_games' => [],
                                'player' => [],
                                'has_empty_seats' => false // 记录是否有空位
                            ];
                        }

                        // 添加子游戏信息
                        $unionGroups[$unionTip]['union_games'][] = [
                            'id' => $v['id'],
                            'name' => $v['name'],
                            'sn' => $v['sn']
                        ];

                        // 如果这个子游戏有空位，标记联机游戏组有空位
                        if ($hasEmptySeats) {
                            $unionGroups[$unionTip]['has_empty_seats'] = true;
                        }

                        // 合并玩家信息
                        foreach ($v['player'] as $player) {
                            $unionGroups[$unionTip]['player'][] = $player;
                        }
                    } else {
                        // 普通游戏：直接添加
                        $v['is_union'] = false;
                        $processedData[] = $v;
                    }
                }

                // 将联机游戏添加到结果中
                foreach ($unionGroups as $unionGame) {
                    // 如果开启了"只看可玩"过滤，检查联机游戏是否有空位
                    if ($only_playable && $user_info) {
                        if (!$unionGame['has_empty_seats']) {
                            continue; // 跳过没有空位的联机游戏
                        }
                    }
                    $processedData[] = $unionGame;
                }
                $this->success('成功获取',$processedData);
            }  catch (\think\exception\HttpResponseException $e) {
                throw $e;
            } catch (\Exception $e) {
                // 记录错误日志
                Log::error('游戏列表获取失败：' . $e->getMessage());
                $this->error('获取游戏列表失败，请稍后重试');
            }
        } else {
            $this->error('请求方式不正确');
        }
    }

    /**
     * 添加域名前缀到图片路径
     * @param string $path 图片路径
     * @return string 完整的图片URL
     */
    private function addDomainToPath($path)
    {
        if (empty($path)) {
            return '';
        }
        return \config('site.domain') . $path;
    }

    /**
     * @Method 获取所有房间信息
     * @Time 2025/8/28
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function list_dcl_ast4()
    {
        // post提交
        if ($this->request->isPost()) {
            try {
                // 查询所有未删除的房间信息
                $data = Db::name('game')
                    ->where('is_delete', 0)
                    ->field('id,heiqiplayer_sender_id,name,c_id')
                    ->select();

                $this->success('成功获取', $data);
            } catch (\think\exception\HttpResponseException $e) {
                throw $e;
            } catch (\Exception $e) {
                // 记录错误日志
                Log::error('房间列表获取失败：' . $e->getMessage());
                $this->error('获取房间列表失败，请稍后重试');
            }
        } else {
            $this->error('请求方式不正确');
        }
    }

    /**
     * @Method 游戏详情
     * @Authod Jw
     * @Time 2021/6/21
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function detail()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();
            $where = [];

            if (!isset($params['gameId']) || empty($params['gameId'])) {
                $this->error('游戏Id不能为空');
            }

            $where['id'] = $params['gameId'];
            $where['is_delete'] = 0;
            $game = Db::name('game')
                ->where($where)
                ->where('status','>',0)
                ->field('id,sn,name,coin,status,players,playermode,heiqiplayer_signaling_url,heiqiplayer_sender_id,description,is_landscape,control_type,control_config,video_rotation')
                ->find();

            if (empty($game)) {
                $this->error('未查找到游戏');
            }
            if ($game['status'] == 2) {
                $this->error('游戏维护中');
            }

            //游戏座位数据
            $game['seats'] = Db::name('game_seat')
                ->alias('s')
                ->join(['fa_game_log l'],'s.id=l.seat_id and l.status in (0,1)','left')
                ->join(['fa_user u'],'u.id=l.user_id','left')
                ->where('s.game_id',$game['id'])
                ->field('s.id,s.game_id,s.number,s.status,u.nickname,u.avatar')
                ->select();
            foreach ($game['seats'] as &$v) {
                $v['avatar'] = config('site.domain').$v['avatar'];
            }
            unset($v);
            
            //游戏中用户
            $game['players'] = Db::name('game_log')
                ->alias('l')
                ->join(['fa_user u'],'l.user_id=u.id','left')
                ->where(['l.game_id'=>$game['id'],'l.status'=>1])
                ->field('l.seat_id,u.nickname,avatar')
                ->select();

            foreach ($game['players'] as $k => &$v) {
                $v['avatar'] = \config('site.domain').$v['avatar'];
            }
            unset($v);

            $game['userInfo'] = Db::name('user')->where('id',$this->auth->id)->field('id,nickname,avatar,money,score')->find();
            $game['userInfo']['avatar'] = $game['userInfo'] ? \config('site.domain').$game['userInfo']['avatar'] : '';


            //判断座位有没有满,0=当前空闲，1=座位已满，2=当前用户正在玩
            $game['is_full'] = 0;

            foreach ($game['seats'] as $k => $v) {//循环座位表，查看状态
                if ($v['status'] == 1) {//进行中
                    $game['is_full'] = 1;
                } else {
                    $game['is_full'] = 0;
                    break;
                }
            }

            //查询当前用户有没有在玩
            $game_log = Db::name('game_log')
                ->where(['game_id'=>$game['id'],'user_id'=>$game['userInfo']['id']])
                ->where('status','in',[0,1])
                ->field('id,status,number')
                ->find();
            $game['gameLogId'] = null;
            if ($game_log) {
                $game['is_full'] = 2;
                $game['gameLogId'] = $game_log['id'];
                $game['number'] = $game_log['number'];
                if ($game_log['status'] == 0) {//0=开始游戏等待响应中
                    $game['is_full'] = -1;//等待响应中...
                }
            }

            //平台客服二维码
            $game['service_qr'] = config('site.domain').config('site.service_qr');

            $this->success('成功获取',$game);
        }else{
            $this->error('请求方式不正确');
        }
    }

    /**
     * 清除未开始的游戏 和 修改设备在线离线状态
     * @Authod Jw
     * @Time 2025/5/16
     * @return string
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * @throws \think\exception\PDOException
     */
    public function delete_log()
    {
        $oneMinuteAgo = time() - 15;

        // 链式查询
        $result = Db::name('game_log')
            ->where('status', 0)                     // 状态为0
            ->where('createtime', '<=', $oneMinuteAgo) // 创建时间超过1分钟前
            ->whereTime('createtime', '>=', '-2 days')  // 获取两天内的数据
            ->select();

        foreach ($result as $index => $v) {
            Db::name('game_log')
                ->where('id',$v['id'])
                ->update(['status'=>-1,'updatetime'=>time()]);

            Db::name('game_seat')
                ->where(['id'=>$v['seat_id'],'status'=>1])
                ->update(['status'=>0,'updatetime'=>time()]);

        }

        $games = Db::name('game')->field('sn')->select();

        foreach ($games as $game) {
            $cacheKey = "device_online:{$game['sn']}";
            $cache = Cache::get($cacheKey);
            if (empty($cache)) {// 缓存不存在 -> 离线状态
                Db::name('game')
                    ->where('sn', $game['sn'])
                    ->update([
                        'device_status' => 0,
                        'offline_time' => date('Y-m-d H:i:s',time())
                    ]);
            }
        }

        return 'ok';
    }

    /**
     * 获取到游戏的SN号
     * <AUTHOR>
     * @date 2025-6-19
     * @return
     */
    public function get_game_sn()
    {
        $game_model = new \app\admin\model\Game();
        $game_model->get_game_sn();
        return 'ok';
    }

    /**
     * 获取配置信息
     * <AUTHOR>
     * @date 2025-8-6
     * @return
     */
    public function get_config()
    {
        $data['exchange'] = config('site.exchange');//兑换比例
        $data['coins_name'] = config('site.coins_name');//coins名称
        $data['point_name'] = config('site.point_name');//兑换名称：如：彩票/积分
        $data['register'] = config('site.register');//注册开关
        $data['recharge'] = config('site.recharge');//充值开关
        $data['invite_rule'] = config('site.invite_rule');//邀请规则

        $this->success('成功获取',$data);
    }

    public function test(){
        $test = Cache::get('game_test');
        if (empty($test)) {
            print_r(12);
        }
        Cache::set('game_test',1,30);
        print_r(2);die;
    }

}

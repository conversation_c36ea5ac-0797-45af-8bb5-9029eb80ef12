<?php
/**
 * 测试新接口 /api/game/list_dcl_ast4
 * 用于获取所有房间信息
 */

// 设置请求URL
$url = 'http://testva2.91jdcd.com/api/game/list_dcl_ast4';

// 准备POST数据
$postData = [];

// 初始化cURL
$ch = curl_init();

// 设置cURL选项
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'Accept: application/json'
]);

// 执行请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

// 检查错误
if (curl_error($ch)) {
    echo "cURL错误: " . curl_error($ch) . "\n";
} else {
    echo "HTTP状态码: " . $httpCode . "\n";
    echo "响应内容: " . $response . "\n";
    
    // 解析JSON响应
    $data = json_decode($response, true);
    if ($data) {
        echo "\n解析后的数据:\n";
        print_r($data);
        
        if (isset($data['data']) && is_array($data['data'])) {
            echo "\n房间数量: " . count($data['data']) . "\n";
            if (!empty($data['data'])) {
                echo "第一个房间信息:\n";
                print_r($data['data'][0]);
            }
        }
    }
}

// 关闭cURL
curl_close($ch);
